"""LangGraph chatbot graph with checkpointing.

A simple chatbot that uses OpenRouter LLM via llm_factory with conversation persistence.
"""

from __future__ import annotations

import sys
from pathlib import Path

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from typing import TypedDict, Any, Dict
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

from catchup.nodes import call_model,  tools_node
from catchup.state import CatchUpState


class Configuration(TypedDict):
    """Configurable parameters for the chatbot."""
    model_name: str
    system_prompt: str


def should_route_to_tools(state: CatchUpState) -> str:
    """Determine whether to route to tools or end the conversation."""
    messages = state.get("messages", [])

    if not messages:
        return END

    last_message = messages[-1]

    if isinstance(last_message, AIMessage):
        if last_message.tool_calls:
            print(f"Found {len(last_message.tool_calls)} valid tool calls")
            return "tools"

        if hasattr(last_message, 'invalid_tool_calls') and last_message.invalid_tool_calls:
            print(f"Found {len(last_message.invalid_tool_calls)} invalid tool calls")
            return "tools"

    return END




# Create checkpointer for conversation persistence
#checkpointer = MemorySaver()



# Define the graph with checkpointing
graph = (
    StateGraph(CatchUpState, config_schema=Configuration)
    .add_node("call_model", call_model)
    .add_node("tools", tools_node)
    .add_edge(START, "call_model")
    .add_conditional_edges("call_model", should_route_to_tools, {
        "tools": "tools",
        END: END  # Use END constant, not string
    })
    .add_edge("tools", "call_model")  # Tools should route back to call_model
    .compile()
)


