"""Tools node for executing MCP tools in the CatchUp customer service system."""

from __future__ import annotations

import async<PERSON>
from typing import Any, Dict
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import ToolNode
from langchain_core.messages import ToolMessage, AIMessage
from langgraph.config import get_stream_writer

from catchup.state import CatchUpState
from catchup.tools_loader import get_all_catchup_tools
from langgraph.types import interrupt
HIL = {
    "whatsapps_sent_tool",
    "get_categories"
}

async def tools_node(state: CatchUpState, config: RunnableConfig) -> Dict[str, Any]:
    """Tools node that executes MCP tools with error handling and streaming."""
    all_tools = await get_all_catchup_tools()
    
    # Get the last message to check for tool calls
    messages = state.get("messages", [])
    if messages:
        last_message = messages[-1]
        if isinstance(last_message, AIMessage) and last_message.tool_calls:
            stream_writer = get_stream_writer()
            
            # Stream notification for each tool being called
            for tool_call in last_message.tool_calls:
                tool_name = tool_call.get("name", "unknown_tool")
                print(f"tool_name: {tool_name}")
                if tool_name in HIL:
                    print(f"Human-in-the-loop tool call detected: {tool_name}")
                    confirmation_message = {
             
                        "message": f"Do you want to run the column ?"
                    }
                     # Request user confirmation
                    user_response = interrupt(confirmation_message)
                     # Check if user confirmed or cancelled
                    if not (user_response and str(user_response).lower() in ['yes', 'y', 'confirm', 'proceed', 'ok', 'run']):
                        # User cancelled - create error message
                        # outputs.append(
                        #     ToolMessage(
                        #         content=f"Column execution cancelled by user. Column '{column_name}' was not run.",
                        #         name=tool_call["name"],
                        #         tool_call_id=tool_call["id"],
                        #     )
                        # )
                        continue  # Skip to next tool call without executing
                    
                
                stream_writer({"custom_tool_call": f"executing {tool_name}"})
    
    custom_tools_node = ToolNode(all_tools)
    
    #await asyncio.sleep(10)
    return await custom_tools_node.ainvoke(state, config)
