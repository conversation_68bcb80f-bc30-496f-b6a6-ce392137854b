"""Shared tools loading functionality for CatchUp agent."""

from typing import List
from langchain_core.tools import BaseTool
from shared.mcp_tools import get_catchup_tools
from catchup.tools.get_user_details import get_all_categories, get_user_details_by_id


async def get_all_catchup_tools() -> List[BaseTool]:
    """Get all available tools for CatchUp (MCP + local tools).
    
    Returns:
        List of all available tools for the CatchUp system
    """
    mcp_tools = await get_catchup_tools()
    local_tools = [get_user_details_by_id, get_all_categories]
    
    all_tools = mcp_tools + local_tools
    
    print(f"Loaded {len(all_tools)} tools in total")
    print(f"Tool names: {[tool.name for tool in all_tools]}")
    
    return all_tools